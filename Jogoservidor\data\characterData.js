// data/characterData.js

export const characters = [
    { name: "<PERSON><PERSON>", image: "/imagens/birdo.jpg" },
    { name: "<PERSON> Bones", image: "/imagens/dry.jpg" },
    { name: "<PERSON> Kong", image: "/imagens/dixie.webp" },
    { name: "<PERSON>", image: "/imagens/pauline.jpg" },
    { name: "<PERSON><PERSON>", image: "/imagens/rosalina.png" },
    { name: "<PERSON><PERSON><PERSON>", image: "/imagens/kamek.png" },
    { name: "<PERSON><PERSON><PERSON>", image: "/imagens/koopa.png" },
    { name: "<PERSON><PERSON>", image: "/imagens/petey.webp" },
    { name: "<PERSON><PERSON><PERSON>", image: "/imagens/goomba.jpg" },
    { name: "Toade<PERSON>", image: "/imagens/toadette.png" },
    { name: "<PERSON>", image: "/imagens/james.webp" },
    { name: "<PERSON><PERSON><PERSON> Head", image: "/imagens/piramid.webp" },
    { name: "<PERSON> Kid", image: "/imagens/skull.jpg" },
    { name: "<PERSON><PERSON><PERSON>", image: "/imagens/kristal.webp" },
    { name: "<PERSON>", image: "/imagens/hammer.jpg" },
    { name: "Guile", image: "/imagens/guile.jpg" },
    { name: "Ghost Pac-man", image: "/imagens/ghost.webp" },
    { name: "Chaim Chomp", image: "/imagens/chaim.webp" },
    { name: "Rain World", image: "/imagens/rain.jpg" },
    { name: "Niko", image: "/imagens/niko.jpg" },
    { name: "Mita", image: "/imagens/mita.jpg" },
    { name: "Mineirinho", image: "/imagens/mineirinho.jpg" },
    { name: "Little Nightmares", image: "/imagens/little.jpg" },
    { name: "Isaac", image: "/imagens/isac.jpg" },
    { name: "Among Us", image: "/imagens/among.webp" },
    { name: "Leon", image: "/imagens/leon.jpg" },
    { name: "Linus", image: "/imagens/linus.jpg" },
    { name: "Robin", image: "/imagens/robin.jpg" },
    { name: "Sebastian", image: "/imagens/sebastian.webp" },
    { name: "Abigail", image: "/imagens/abigail.webp" },
    { name: "Lewis", image: "/imagens/lewis.webp" },
    { name: "Ratchet", image: "/imagens/ratchet.jpg" },
    { name: "Klonoa", image: "/imagens/klonoa.jpg" },
    { name: "Meta Knight", image: "/imagens/meta.jpg" },
    { name: "Foxy", image: "/imagens/foxy.jpg" },
    { name: "Bonnie", image: "/imagens/bonnie.webp" },
    { name: "Chica", image: "/imagens/chica.webp" },
    { name: "Freddy", image: "/imagens/freddy.webp" },
    { name: "Inside", image: "/imagens/inside.jpg" },
    { name: "Gris", image: "/imagens/gris.png" },
    { name: "Demonio", image: "/imagens/demonio.webp" },
    { name: "Caneco", image: "/imagens/caneco.webp" },
    { name: "Tiny Tiger", image: "/imagens/tinytiger.jpg" },
    { name: "Coco", image: "/imagens/coco.webp" },
    { name: "Cortex", image: "/imagens/cortex.jpg" },
    { name: "Knight", image: "/imagens/knight.jpeg" },
    { name: "Macaco Engenheiro", image: "/imagens/macaco.webp" },
    { name: "Mae", image: "/imagens/mae.webp" },
    { name: "Blaze", image: "/imagens/blaze.webp" },
    { name: "Rouge", image: "/imagens/rouge.jpg" },
    { name: "Amy", image: "/imagens/amy.webp" },
    { name: "Absol", image: "/imagens/absol.webp" },
    { name: "Umbreon", image: "/imagens/umbreon.webp" },
    { name: "Espeon", image: "/imagens/espeon.webp" },
    { name: "Flareon", image: "/imagens/flareon.jpg" },
    { name: "Jolteon", image: "/imagens/jolteon.webp" },
    { name: "Vaporeon", image: "/imagens/vaporeon.jpeg" },
    { name: "Ditto", image: "/imagens/ditto.webp" },
    { name: "Raichu", image: "/imagens/raichu.jpg" },
    { name: "Dragonite", image: "/imagens/dragonite.jpg" },
    { name: "Blastoise", image: "/imagens/blastoise.jpeg" },
    { name: "Snorlax", image: "/imagens/snorlax.jpeg" },
    { name: "Gardevoir", image: "/imagens/gardevoir.webp" },
    { name: "Garchomp", image: "/imagens/garchomp.webp" },
    { name: "Lucario", image: "/imagens/lucario.jpg" },
    { name: "Mew", image: "/imagens/mew.jpg" },
    { name: "Squirtle", image: "/imagens/squirtle.jpeg" },
    { name: "Charmander", image: "/imagens/charmander.webp" },
    { name: "Bulbasaur", image: "/imagens/bulbasaur.webp" },
    { name: "Eevee", image: "/imagens/eevee.jpg" },
    { name: "Charizard", image: "/imagens/charizard.jpg" },
    { name: "Tommy Vercetti", image: "/imagens/tommy_vercetti.webp" },
    { name: "Shy Guy", image: "/imagens/shy_guy.webp" },
    { name: "Papyrus", image: "/imagens/papyrus.jpg" },
    { name: "Little Mac", image: "/imagens/little_mac.webp" },
    { name: "Liu Kang", image: "/imagens/liu_kang.jpg" },
    { name: "King Boo", image: "/imagens/king_boo.png" },
    { name: "Knuckles", image: "/imagens/knuckles.jpg" },
    { name: "King Dedede", image: "/imagens/king_dedede.webp" },
    { name: "Duke Nukem", image: "/imagens/duke_nukem.webp" },
    { name: "Diddy Kong", image: "/imagens/diddy_kong.png" },
    { name: "Cranky Kong", image: "/imagens/cranky_kong.png" },
    { name: "Conker", image: "/imagens/conker.jpg" },
    { name: "Sub Zero", image: "/imagens/subzero.jpg" },
    { name: "Scorpion", image: "/imagens/scorpion.png" },
    { name: "Captain Toad", image: "/imagens/captain_toad.png" },
    { name: "Shantae", image: "/imagens/shantae.jpeg" },
    { name: "Geno", image: "/imagens/geno.webp" },
    { name: "Funky Kong", image: "/imagens/funky_kong.jpeg" },
    { name: "Silver", image: "/imagens/silver.webp" },
    { name: "Joker", image: "/imagens/joker.jpg" },
    { name: "Piranha Plant", image: "/imagens/piranha_plant.webp" },
    { name: "Incineroar", image: "/imagens/incineroar.jpeg" },
    { name: "King K. Rool", image: "/imagens/king_k_rool.jpg" },
    { name: "Inkling", image: "/imagens/inkling.webp" },
    { name: "Cloud", image: "/imagens/cloud.webp" },
    { name: "Ken", image: "/imagens/ken.jpeg" },
    { name: "Duck Hunt", image: "/imagens/duck_hunt.png" },
    { name: "Bowser Jr.", image: "/imagens/bowser_jr.jpg" }
];

export const uniqueCharacters = characters.filter((char, index, self) =>
    index === self.findIndex((c) => c.name === char.name)
);

export const nintendoCharacters = [
    'Bowser Jr.', 'Duck Hunt', 'Ken', 'Cloud', 'Inkling', 'King K. Rool', 'Incineroar',
    'Piranha Plant', 'Joker', 'Silver', 'Funky Kong', 'Geno', 'Shantae', 'Captain Toad',
    'Scorpion', 'Sub Zero', 'Conker', 'Cranky Kong', 'Diddy Kong', 'Duke Nukem',
    'King Dedede', 'Knuckles', 'King Boo', 'Liu Kang', 'Little Mac', 'Papyrus',
    'Shy Guy', 'Tommy Vercetti', 'Charizard', 'Eevee', 'Bulbasaur', 'Charmander',
    'Squirtle', 'Mew', 'Lucario', 'Garchomp', 'Gardevoir', 'Snorlax', 'Blastoise',
    'Dragonite', 'Raichu', 'Ditto', 'Vaporeon', 'Jolteon', 'Flareon', 'Espeon',
    'Umbreon', 'Absol', 'Amy', 'Rouge', 'Blaze', 'Mae', 'Macaco Engenheiro',
    'Knight', 'Cortex', 'Coco', 'Tiny Tiger', 'Caneco', 'Demonio', 'Gris',
    'Inside', 'Freddy', 'Chica', 'Bonnie', 'Foxy', 'Meta Knight', 'Klonoa',
    'Ratchet', 'Lewis', 'Robin', 'Sebastian', 'Abigail', 'Linus', 'Leon',
    'Among Us', 'Isaac', 'Little Nightmares', 'Mineirinho', 'Mita', 'Niko',
    'Rain World', 'Chaim Chomp', 'Ghost Pac-man', 'Guile', 'Hammer Bro',
    'Krystal', 'Skull Kid', 'Piramid Head', 'James', 'Toadette', 'Goomba',
    'Koopa', 'Petey Piranha', 'Kamek', 'Rosalina', 'Pauline', 'Dixie Kong',
    'Dry Bones', 'Birdo'
];

export const anthropomorphicCharacters = [
    'Charizard', 'Eevee', 'Bulbasaur', 'Charmander', 'Squirtle', 'Mew', 'Lucario',
    'Garchomp', 'Gardevoir', 'Snorlax', 'Blastoise', 'Dragonite', 'Raichu',
    'Ditto', 'Vaporeon', 'Jolteon', 'Flareon', 'Espeon', 'Umbreon', 'Absol',
    'Amy', 'Rouge', 'Blaze', 'Mae', 'Macaco Engenheiro', 'Knight', 'Cortex',
    'Coco', 'Tiny Tiger', 'Caneco', 'Demonio', 'Gris', 'Inside', 'Freddy',
    'Chica', 'Bonnie', 'Foxy', 'Meta Knight', 'Klonoa', 'Ratchet', 'Lewis',
    'Robin', 'Sebastian', 'Abigail', 'Linus', 'Leon', 'Among Us', 'Isaac',
    'Little Nightmares', 'Mineirinho', 'Mita', 'Niko', 'Rain World', 'Chaim Chomp',
    'Ghost Pac-man', 'Guile', 'Hammer Bro', 'Krystal', 'Skull Kid', 'Piramid Head',
    'James', 'Toadette', 'Goomba', 'Koopa', 'Petey Piranha', 'Kamek', 'Rosalina',
    'Pauline', 'Dixie Kong', 'Dry Bones', 'Birdo'
];