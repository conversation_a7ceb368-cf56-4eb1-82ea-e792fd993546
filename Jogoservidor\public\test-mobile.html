<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover, shrink-to-fit=no">
  <title>Teste Mobile - Character Clash</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background: #1a1a1a;
      color: white;
    }
    .test-section {
      margin: 20px 0;
      padding: 15px;
      background: rgba(255,255,255,0.1);
      border-radius: 10px;
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 5px;
    }
    .success { background: #4caf50; }
    .error { background: #f44336; }
    .warning { background: #ff9800; }
    .info { background: #2196f3; }
    button {
      padding: 10px 20px;
      margin: 5px;
      border: none;
      border-radius: 5px;
      background: #4a90e2;
      color: white;
      cursor: pointer;
    }
    button:hover {
      background: #357abd;
    }
    .grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      margin: 10px 0;
    }
    .card {
      background: rgba(255,255,255,0.1);
      padding: 10px;
      border-radius: 5px;
      text-align: center;
    }
  </style>
</head>
<body>
  <h1>🧪 Teste Mobile - Character Clash</h1>
  
  <div class="test-section">
    <h2>📱 Informações do Dispositivo</h2>
    <div id="device-info"></div>
  </div>
  
  <div class="test-section">
    <h2>🔧 Testes de Funcionalidade</h2>
    <button onclick="testDataLoading()">Testar Carregamento de Dados</button>
    <button onclick="testViewport()">Testar Viewport</button>
    <button onclick="testTouch()">Testar Touch</button>
    <button onclick="testImages()">Testar Imagens</button>
    <div id="test-results"></div>
  </div>
  
  <div class="test-section">
    <h2>🎮 Teste de Layout</h2>
    <div class="grid">
      <div class="card">Card 1</div>
      <div class="card">Card 2</div>
      <div class="card">Card 3</div>
      <div class="card">Card 4</div>
    </div>
  </div>
  
  <div class="test-section">
    <h2>🔗 Links de Teste</h2>
    <button onclick="window.location.href='index.html'">Ir para o Jogo</button>
    <button onclick="window.location.href='index.html?test=mobile'">Jogo com Debug Mobile</button>
  </div>

  <script>
    // Detectar informações do dispositivo
    function getDeviceInfo() {
      const info = {
        userAgent: navigator.userAgent,
        screenWidth: screen.width,
        screenHeight: screen.height,
        windowWidth: window.innerWidth,
        windowHeight: window.innerHeight,
        devicePixelRatio: window.devicePixelRatio,
        touchSupport: 'ontouchstart' in window,
        orientation: screen.orientation ? screen.orientation.angle : 'unknown',
        platform: navigator.platform,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine
      };
      
      const infoDiv = document.getElementById('device-info');
      infoDiv.innerHTML = Object.entries(info)
        .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
        .join('<br>');
    }
    
    function addResult(message, type = 'info') {
      const resultsDiv = document.getElementById('test-results');
      const div = document.createElement('div');
      div.className = `status ${type}`;
      div.innerHTML = message;
      resultsDiv.appendChild(div);
    }
    
    function testDataLoading() {
      addResult('🔄 Testando carregamento de dados...', 'info');
      
      // Testar se os scripts carregaram
      const hasCharacters = typeof window.characters !== 'undefined';
      const hasHelpers = typeof window.shuffleArray !== 'undefined';
      
      if (hasCharacters && hasHelpers) {
        addResult('✅ Dados carregados com sucesso!', 'success');
        addResult(`📊 ${window.characters.length} personagens carregados`, 'info');
      } else {
        addResult('❌ Erro no carregamento dos dados', 'error');
        addResult(`Characters: ${hasCharacters}, Helpers: ${hasHelpers}`, 'warning');
      }
    }
    
    function testViewport() {
      addResult('🔄 Testando viewport...', 'info');
      
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
      
      addResult(`📐 Viewport: ${window.innerWidth}x${window.innerHeight}`, 'info');
      addResult(`📱 CSS vh: ${vh}px`, 'info');
      addResult('✅ Viewport configurado', 'success');
    }
    
    function testTouch() {
      addResult('🔄 Testando suporte a touch...', 'info');
      
      const hasTouch = 'ontouchstart' in window;
      const hasPointer = navigator.maxTouchPoints > 0;
      
      if (hasTouch || hasPointer) {
        addResult('✅ Suporte a touch detectado', 'success');
        addResult(`Touch events: ${hasTouch}, Max touch points: ${navigator.maxTouchPoints}`, 'info');
      } else {
        addResult('⚠️ Suporte a touch não detectado', 'warning');
      }
    }
    
    function testImages() {
      addResult('🔄 Testando carregamento de imagens...', 'info');
      
      const testImg = new Image();
      testImg.onload = () => {
        addResult('✅ Imagens carregando corretamente', 'success');
      };
      testImg.onerror = () => {
        addResult('❌ Erro no carregamento de imagens', 'error');
      };
      testImg.src = '/imagens/mario.jpeg';
    }
    
    // Executar testes iniciais
    document.addEventListener('DOMContentLoaded', () => {
      getDeviceInfo();
      
      // Auto-teste após 1 segundo
      setTimeout(() => {
        testDataLoading();
        testViewport();
        testTouch();
        testImages();
      }, 1000);
    });
  </script>
</body>
</html>
